
import { useState, useEffect } from 'react';
// TODO: Replace with Amplify data layer
// import { supabase } from '@/integrations/supabase/client';
import { useAmplifyAuth } from './useAmplifyAuth';
import { Company } from '@/types/onboarding';

export interface DatabaseCompany {
  id: string;
  name: string;
  industry: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export function useCompany() {
  const { user } = useAmplifyAuth();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchCompany();
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchCompany = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement company fetch with Amplify data layer');
      setCompany(null);

    } catch (err) {
      console.error('Error fetching company:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch company');
    } finally {
      setLoading(false);
    }
  };

  const createCompany = async (companyData: Omit<Company, 'id' | 'createdBy' | 'createdAt'>) => {
    try {
      if (!user) throw new Error('User not authenticated');

      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement company creation with Amplify data layer');

      const company: Company = {
        id: 'temp-id',
        name: companyData.name,
        industry: companyData.industry,
        createdBy: user.userId || '',
        createdAt: new Date()
      };

      setCompany(company);
      return company;
    } catch (err) {
      console.error('Error creating company:', err);
      throw err;
    }
  };

  return {
    company,
    loading,
    error,
    createCompany,
    refetch: fetchCompany
  };
}
