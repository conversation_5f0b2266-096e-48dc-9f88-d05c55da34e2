version: 1
backend:
  phases:
    build:
      commands:
        - npm install -g pnpm@10.12.3
        - cd apps/backend
        - pnpm install
        - npx amplify generate outputs --app-id $AWS_APP_ID --branch $AWS_BRANCH
frontend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm@10.12.3
        - pnpm install --frozen-lockfile
        # Copy Amplify outputs to web app (with error handling)
        - |
          if [ -f "apps/backend/amplify_outputs.json" ]; then
            cp apps/backend/amplify_outputs.json apps/web/src/amplify_outputs.json
            echo "Successfully copied amplify_outputs.json"
          else
            echo "Warning: amplify_outputs.json not found in apps/backend/"
            echo "Creating placeholder file to prevent build failure"
            echo '{"version":"1.4","auth":{},"data":{}}' > apps/web/src/amplify_outputs.json
          fi
    build:
      commands:
        - pnpm turbo run build --filter=@convx/web
  artifacts:
    baseDirectory: apps/web/dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/.pnpm-store/**/*
