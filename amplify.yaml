version: 1
backend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm@10.12.3
    build:
      commands:
        - cd apps/backend
        - pnpm install --frozen-lockfile
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
frontend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm@10.12.3
    build:
      commands:
        - pnpm install --frozen-lockfile
        # Copy Amplify outputs to web app
        - |
          if [ -f "apps/backend/amplify_outputs.json" ]; then
            mkdir -p apps/web/src
            cp apps/backend/amplify_outputs.json apps/web/src/amplify_outputs.json
            echo "Successfully copied amplify_outputs.json"
          else
            echo "Warning: amplify_outputs.json not found in apps/backend/"
            exit 1
          fi
        - pnpm turbo run build --filter=@convx/web
  artifacts:
    baseDirectory: apps/web/dist
    files:
      - '**/*'
  cache:
    paths:
      - .pnpm-store/**/*
