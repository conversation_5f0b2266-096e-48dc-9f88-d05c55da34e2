# name: CI

# on:
#   push:
#     branches: [main, develop]
#   pull_request:
#     branches: [main, develop]

# jobs:
#   lint-and-test:
#     runs-on: ubuntu-latest

#     strategy:
#       matrix:
#         node-version: [22]

#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4

#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: ${{ matrix.node-version }}

#       - name: Setup pnpm
#         uses: pnpm/action-setup@v4
#         with:
#           version: 10.12.3

#       - name: Get pnpm store directory
#         shell: bash
#         run: |
#           echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

#       - name: Setup pnpm cache
#         uses: actions/cache@v4
#         with:
#           path: ${{ env.STORE_PATH }}
#           key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
#           restore-keys: |
#             ${{ runner.os }}-pnpm-store-

#       - name: Install dependencies
#         run: pnpm install --frozen-lockfile

#       - name: Lint
#         run: pnpm lint

#       - name: Build
#         run: pnpm build

#       - name: Test
#         run: pnpm test

#   commitlint:
#     runs-on: ubuntu-latest
#     if: github.event_name == 'pull_request'

#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#         with:
#           fetch-depth: 0

#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: 22

#       - name: Setup pnpm
#         uses: pnpm/action-setup@v4
#         with:
#           version: 10.12.3

#       - name: Install dependencies
#         run: pnpm install --frozen-lockfile

#       - name: Validate commit messages
#         run: npx commitlint --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }} --verbose
